import User from "../models/User.js";
import Dish from "../models/Dish.js";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import mongoose from "mongoose";
import {
  generateRecommendation,
  generateVectorSearchPrompt,
} from "../services/butler-service.js";
import Conversation from "../models/Conversation.js";
import Order from "../models/Order.js";
import Outlet from "../models/Outlet.js";
import { emitNewOrder } from "../sockets/orderSocket.js";
import ollama from "ollama";
import {
  emitOrderStatusUpdate,
  emitOrderItemsUpdate,
} from "../sockets/orderSocket.js";
import { reapplyCouponToOrder } from "./coupon-controller.js";
import { updatePaymentLink } from "../utils/razorpay.js";
import Payment from "../models/Payment.js";
import {
  detectCartOperation,
  processCartOperation,
} from "../services/cart-service.js";
import { validateAndApplyOffers } from "../services/offer-validation-service.js";
import CartOperation from "../models/CartOperation.js";
import {
  searchDishes,
  getRecommendations as getMenuRecommendations,
  searchWithCategories,
} from "../services/menu-search-service.js";
import {
  personalizeMenuOrder,
  getPersonalizedRecommendations,
  getUserPreferences,
} from "../services/personalization-service.js";
import {
  getComprehensiveContext,
  generateContextPrompt,
} from "../services/context-service.js";
import { generateTextStream } from "../services/groq-service.js";

export const register = async (req, res) => {
  try {
    const {
      email,
      password,
      name,
      phone,
      address = "",
      aiMessage = "",
    } = req.body;

    if (!email || !password || !name) {
      return res
        .status(400)
        .json({ message: "Email, password and name are required" });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create new user
    const user = new User({
      email,
      password: hashedPassword,
      name,
      phone,
      address,
      role: "user",
    });

    await user.save();

    // Generate token
    const token = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET
    );

    res.status(201).json({
      success: true,
      message: "User registered successfully",
      data: {
        token,
        user: {
          email: user.email,
          name: user.name,
          role: user.role,
          id: user._id,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error registering user",
      error: error.message,
    });
  }
};

export const registerWithPhone = async (req, res) => {
  try {
    const { phone, name, address = "", aiMessage = "" } = req.body;

    if (!phone || !name) {
      return res
        .status(400)
        .json({ message: "Phone number and name are required" });
    }

    // Check if user already exists with this phone number
    const existingUser = await User.findOne({ phone });
    if (existingUser) {
      return res
        .status(400)
        .json({ message: "User with this phone number already exists" });
    }

    // Create new user without password (phone-based registration)
    const user = new User({
      name,
      phone,
      address,
      role: "user",
      createdBy: "self",
    });

    await user.save();

    // Generate token
    const token = jwt.sign(
      { userId: user._id, phone: user.phone },
      process.env.JWT_SECRET
    );

    res.status(201).json({
      success: true,
      message: "User registered successfully",
      data: {
        token,
        user: {
          phone: user.phone,
          name: user.name,
          role: user.role,
          id: user._id,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error registering user",
      error: error.message,
    });
  }
};

export const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res
        .status(400)
        .json({ message: "Email and password are required" });
    }

    // Find user
    const user = await User.findOne({ email });
    if (!user || user.role !== "user") {
      return res.status(404).json({ message: "User not found" });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: "Invalid password" });
    }

    // Update last login time
    user.lastLogin = new Date();

    // Check if this is the first login for a user created by admin
    const isFirstTimeLogin = user.isFirstLogin === true;

    // If it's not the first login, clear the flag
    if (user.isFirstLogin) {
      user.isFirstLogin = false;
    }

    await user.save();

    // Generate token
    const token = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET
    );

    res.status(200).json({
      success: true,
      data: {
        token,
        user: {
          email: user.email,
          name: user.name,
          role: user.role,
          id: user._id,
          isFirstTimeLogin,
          createdBy: user.createdBy,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error logging in",
      error: error.message,
    });
  }
};

export const loginWithPhone = async (req, res) => {
  try {
    const { phone } = req.body;

    if (!phone) {
      return res.status(400).json({ message: "Phone number is required" });
    }

    // Find user by phone number
    const user = await User.findOne({ phone, role: "user" });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Update last login time
    user.lastLogin = new Date();
    await user.save();

    // Generate token
    const token = jwt.sign(
      { userId: user._id, phone: user.phone },
      process.env.JWT_SECRET
    );

    res.status(200).json({
      success: true,
      data: {
        token,
        user: {
          phone: user.phone,
          name: user.name,
          role: user.role,
          id: user._id,
          createdBy: user.createdBy,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error logging in",
      error: error.message,
    });
  }
};

export const getDishes = async (req, res) => {
  try {
    const {
      foodChainId,
      categoryId,
      outletId,
      includeUnavailable,
      userId,
      personalize,
    } = req.query;
    const query = { foodChain: foodChainId };

    // Only filter by availability if includeUnavailable is not set to 'true'
    if (includeUnavailable !== "true") {
      query.isAvailable = true;
    }

    if (categoryId) {
      query.category = categoryId;
    }

    // If outletId is provided, filter dishes by outlet
    if (outletId) {
      query.outlets = outletId;
    }
    console.log(query, "query");
    // Get all dishes that match the query
    const allDishes = await Dish.find(query)
      .populate("category", "name")
      .select("-__v");

    // If includeUnavailable is true, return all dishes without time-based filtering
    if (includeUnavailable === "true") {
      // Apply personalization if requested and userId is provided
      if (personalize === "true" && userId) {
        try {
          const personalizedDishes = await personalizeMenuOrder(
            allDishes,
            userId,
            outletId
          );
          return res.status(200).json({
            success: true,
            data: personalizedDishes,
          });
        } catch (personalizationError) {
          console.error("Personalization error:", personalizationError);
          // Fall back to non-personalized dishes
        }
      }

      return res.status(200).json({
        success: true,
        data: allDishes,
      });
    }

    // Filter dishes by time availability if needed
    const currentTime = new Date();
    const currentHour = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();
    const currentTimeString = `${currentHour
      .toString()
      .padStart(2, "0")}:${currentMinutes.toString().padStart(2, "0")}`;
    const currentDay = currentTime.getDay(); // 0 = Sunday, 6 = Saturday

    // Apply time filtering for user-facing views
    const dishes = allDishes.filter((dish) => {
      // If dish has no time availability settings, it's available all the time
      if (!dish.timeAvailability) return true;

      // Check if the dish is available on the current day of the week
      if (
        dish.timeAvailability.daysOfWeek &&
        !dish.timeAvailability.daysOfWeek.includes(currentDay)
      ) {
        return false;
      }

      // If using custom hours, check if current time is within the range
      if (dish.timeAvailability.customHours) {
        const startTime = dish.timeAvailability.startTime || "00:00";
        const endTime = dish.timeAvailability.endTime || "23:59";
        return currentTimeString >= startTime && currentTimeString <= endTime;
      }

      // Otherwise, check if it's available during breakfast, lunch, or dinner
      // Breakfast: 6:00 - 11:00
      // Lunch: 11:00 - 16:00
      // Dinner: 16:00 - 0:00 (extended to midnight)
      // Late night: 0:00 - 6:00

      // Get the current hour in 24-hour format
      const hour24 = currentHour;

      if (hour24 >= 6 && hour24 < 11) {
        // Breakfast time
        return dish.timeAvailability.breakfast;
      } else if (hour24 >= 11 && hour24 < 16) {
        // Lunch time
        return dish.timeAvailability.lunch;
      } else if (hour24 >= 16 && hour24 < 24) {
        // Dinner time (extended to midnight)
        return dish.timeAvailability.dinner;
      } else if (hour24 >= 0 && hour24 < 6) {
        // Late night hours (midnight to 6 AM)
        // For late night, show dishes that are either:
        // 1. Available for dinner (extending dinner service)
        // 2. Available for breakfast (early breakfast service)
        // 3. Available for all meal times
        return (
          dish.timeAvailability.dinner ||
          (hour24 >= 4 && dish.timeAvailability.breakfast) ||
          (dish.timeAvailability.breakfast &&
            dish.timeAvailability.lunch &&
            dish.timeAvailability.dinner)
        );
      }

      // Default fallback (should not reach here in normal circumstances)
      return true;
    });

    // Apply personalization if requested and userId is provided
    let finalDishes = dishes;
    if (personalize === "true" && userId) {
      try {
        finalDishes = await personalizeMenuOrder(dishes, userId, outletId);
      } catch (personalizationError) {
        console.error("Personalization error:", personalizationError);
        // Fall back to non-personalized dishes
        finalDishes = dishes;
      }
    }

    res.status(200).json({
      success: true,
      data: finalDishes,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching dishes",
      error: error.message,
    });
  }
};

export const getRecommendations = async (req, res) => {
  try {
    const {
      foodChainId,
      message,
      lastConversation,
      userId,
      outletId,
      language,
    } = req.query;

    // Set streaming headers
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");

    let conversation = await Conversation.findOne({ userId, outletId });

    if (!conversation) {
      conversation = new Conversation({
        userId,
        outletId,
        messages: [],
        foodChainId,
      });
    }

    conversation.messages.push({
      message,
      sender: "user",
      time: new Date(),
    });

    // Get dishes for this food chain
    const availableDishes = await Dish.find({
      foodChain: foodChainId,
      isAvailable: true,
      outlets: outletId,
    }).populate("category", "name");

    // Get recent cart operations for context
    const recentCartOperations = await CartOperation.find({
      userId,
      conversationId: conversation._id.toString(),
    })
      .sort({ createdAt: -1 })
      .limit(5);

    // Get comprehensive context for enhanced recommendations
    const comprehensiveContext = await getComprehensiveContext(
      userId,
      outletId,
      recentCartOperations,
      { includePersonalization: true, includeSession: true }
    );

    // Check if the user message contains a cart operation intent
    const cartOperation = detectCartOperation(message);
    let operationResult = null;

    if (cartOperation) {
      operationResult = await processCartOperation(
        cartOperation,
        userId,
        conversation._id.toString(),
        foodChainId,
        outletId
      );
    }

    // Use enhanced menu search instead of vector search
    let searchResults = [];
    try {
      searchResults = await searchDishes(message, availableDishes, {
        limit: 5,
        threshold: 0.1,
        filters: { availableOnly: true },
      });
    } catch (searchError) {
      console.error("Menu search error:", searchError);
      // Continue without search results if there's an error
    }

    // Choose which prompt to use based on whether we have search results
    let prompt;
    const user = await User.findById(userId).select("aiMessage");
    if (searchResults.length > 0) {
      // Use search results in vector search prompt format for consistency
      const formattedResults = searchResults.map((dish) => ({
        dish: dish,
        similarity: dish.relevanceScore || 0.5,
        metadata: {
          name: dish.name,
          description: dish.description,
          category: dish.category?.name || "",
          price: dish.price,
          tags: dish.tags || [],
          cuisine: dish.cuisine || "",
        },
      }));

      prompt = await generateVectorSearchPrompt(
        message,
        JSON.stringify(formattedResults),
        lastConversation,
        recentCartOperations,
        operationResult,
        comprehensiveContext,
        language
      );
    } else {
      prompt = await generateRecommendation(
        message,
        JSON.stringify(availableDishes),
        lastConversation,
        recentCartOperations,
        operationResult,
        user.aiMessage,
        comprehensiveContext,
        language
      );
    }

    // Initialize variables to collect the response
    let fullResponse = "";

    // Use Groq's streaming API
    await generateTextStream(
      prompt,
      (chunk) => {
        fullResponse += chunk;
      },
      {
        model: "llama3-70b-8192",
        max_tokens: 300, // Increased to ensure complete JSON responses
        temperature: 0.7,
        top_p: 0.95,
        top_k: 40,
      }
    );

    // Create a response object that mimics the HuggingFace response format
    const response = {
      generated_text: fullResponse,
    };

    // Extract and parse JSON more safely
    let aiResponse;
    try {
      console.log("Raw AI response:", response.generated_text);

      // Try to find a complete JSON object in the response
      // First, clean up the response text
      let responseText = response.generated_text
        .replace(/^.*?Here is the response:\s*/i, "") // Remove "Here is the response:" prefix
        .replace(/^.*?```json\s*/i, "") // Remove everything before ```json
        .replace(/^.*?```\s*/i, "") // Remove everything before ```
        .replace(/```.*$/s, "") // Remove everything after ```
        .trim();

      // Look for the pattern that starts with { - even if incomplete
      const jsonRegex = /(\{[\s\S]*)/;
      const jsonMatch = responseText.match(jsonRegex);

      if (!jsonMatch) {
        console.error("No JSON structure found in response");
        console.error("Cleaned response text:", responseText);
        throw new Error("No JSON found in response");
      }

      // Clean the JSON string - be more careful with the extraction
      let cleanJson = jsonMatch[0];

      // Remove any text before the first { and after the last }
      const firstBrace = cleanJson.indexOf("{");
      const lastBrace = cleanJson.lastIndexOf("}");
      if (firstBrace !== -1 && lastBrace !== -1) {
        cleanJson = cleanJson.substring(firstBrace, lastBrace + 1);
      }

      // Further clean the JSON
      cleanJson = cleanJson
        .replace(/[\r\n]/g, " ") // Remove newlines
        .replace(/\s+/g, " ") // Normalize spaces
        .replace(/,\s*}/g, "}") // Remove trailing commas
        .replace(/,\s*]/g, "]") // Remove trailing commas in arrays
        .replace(/"\s*$/, '"') // Fix incomplete quotes at end
        .trim(); // Remove leading/trailing spaces

      // Check if JSON is incomplete and try to fix it
      if (!cleanJson.endsWith("}")) {
        // Handle incomplete JSON more robustly

        // Check for unterminated strings
        const quotes = (cleanJson.match(/"/g) || []).length;
        if (quotes % 2 !== 0) {
          // Odd number of quotes means unterminated string
          cleanJson += '"';
        }

        // Try to find the last complete property and close the JSON
        const lastCommaIndex = cleanJson.lastIndexOf(",");
        const lastColonIndex = cleanJson.lastIndexOf(":");
        const lastQuoteIndex = cleanJson.lastIndexOf('"');

        if (
          lastColonIndex > lastCommaIndex &&
          lastQuoteIndex < lastColonIndex
        ) {
          // Incomplete property value after colon, remove the incomplete property
          const propertyStart = cleanJson.lastIndexOf('"', lastColonIndex - 1);
          if (propertyStart > 0) {
            const beforeProperty = cleanJson.substring(0, propertyStart - 1);
            cleanJson = beforeProperty.endsWith(",")
              ? beforeProperty.slice(0, -1) + "}"
              : beforeProperty + "}";
          }
        } else if (
          lastQuoteIndex > lastCommaIndex &&
          lastQuoteIndex > lastColonIndex
        ) {
          // Incomplete property value, remove it and close JSON
          const propertyStart = cleanJson.lastIndexOf('"', lastQuoteIndex - 1);
          if (propertyStart > 0) {
            const beforeProperty = cleanJson.substring(0, propertyStart - 1);
            cleanJson = beforeProperty.endsWith(",")
              ? beforeProperty.slice(0, -1) + "}"
              : beforeProperty + "}";
          }
        } else {
          // Just close the JSON
          cleanJson += "}";
        }
      }

      console.log("Cleaned JSON:", cleanJson);

      // Try to parse the JSON
      aiResponse = JSON.parse(cleanJson);
      console.log("Parsed AI response:", aiResponse);

      // Function to translate keywords to English if they're in Hindi
      const translateKeywordsToEnglish = (keywords) => {
        const hindiToEnglish = {
          "व्हाइट सॉस पास्ता": "white sauce pasta",
          कार्ट: "cart",
          पास्ता: "pasta",
          पिज्जा: "pizza",
          बर्गर: "burger",
          चिकन: "chicken",
          वेज: "vegetarian",
          "नॉन-वेज": "non-vegetarian",
          शाकाहारी: "vegetarian",
          मांसाहारी: "non-vegetarian",
          वेजिटेरियन: "vegetarian",
          "नॉन-वेजिटेरियन": "non-vegetarian",
          मांस: "meat",
          मछली: "fish",
          मटन: "mutton",
          मसालेदार: "spicy",
          मिठाई: "sweet",
          खाना: "food",
          भोजन: "meal",
          नाश्ता: "breakfast",
          "दोपहर का खाना": "lunch",
          "रात का खाना": "dinner",
          स्नैक्स: "snacks",
        };

        return keywords.map((keyword) => {
          const lowerKeyword = keyword.toLowerCase();
          // Check if it's Hindi and translate
          for (const [hindi, english] of Object.entries(hindiToEnglish)) {
            if (lowerKeyword.includes(hindi.toLowerCase())) {
              return english;
            }
          }
          // If not Hindi or no translation found, return as is (assuming it's English)
          return keyword.toLowerCase();
        });
      };

      // Ensure required properties exist with proper validation
      aiResponse = {
        keywords: Array.isArray(aiResponse.keywords)
          ? translateKeywordsToEnglish(aiResponse.keywords)
          : ["food", "menu"],
        aiMessage:
          typeof aiResponse.aiMessage === "string" &&
          aiResponse.aiMessage.trim() !== ""
            ? aiResponse.aiMessage
            : "How may I assist you with your order?",
        faqSuggestions:
          Array.isArray(aiResponse.faqSuggestions) &&
          aiResponse.faqSuggestions.length > 0
            ? aiResponse.faqSuggestions
            : ["What are your specialties?", "What do you recommend?"],
        cartIntent:
          aiResponse.cartIntent && typeof aiResponse.cartIntent === "object"
            ? aiResponse.cartIntent
            : { detected: false },
      };
    } catch (parseError) {
      console.error("AI response parsing error:", parseError);
      console.error("Error details:", parseError.message);
      console.error("Response that failed parsing:", response.generated_text);

      // Fallback response
      aiResponse = {
        keywords: ["food", "meal", "menu"],
        aiMessage:
          "I'm here to help with your food order. What would you like to try today?",
        faqSuggestions: [
          "What are your specialties?",
          "What do you recommend?",
          "Show me the menu",
        ],
        cartIntent: { detected: false },
      };
    }

    // If we have a successful cart operation, modify the AI response
    if (operationResult && operationResult.success) {
      // If cart operation was successful, add this information to the AI response
      aiResponse.aiMessage = `${operationResult.message}. ${aiResponse.aiMessage}`;

      // Add cart operation to the AI response
      aiResponse.cartOperation = operationResult;
    }

    // Add butler response to conversation
    conversation.messages.push({
      message: aiResponse.aiMessage,
      sender: "butler",
      time: new Date(),
      suggestedQuestions: aiResponse.faqSuggestions,
    });

    // Save conversation
    await conversation.save();

    // Prepare recommendations - prioritize search results if available
    let recommendations = [];

    if (searchResults.length > 0) {
      // Use enhanced search results with dietary preference filtering
      recommendations = searchResults
        .filter((dish) => {
          if (!dish || !dish._id) return false; // Filter out invalid dishes

          // Apply dietary preference filtering based on AI keywords
          if (aiResponse.keywords && aiResponse.keywords.length > 0) {
            const hasVegKeyword = aiResponse.keywords.some(
              (keyword) =>
                keyword.toLowerCase().includes("vegetarian") ||
                keyword.toLowerCase().includes("veg")
            );
            const hasNonVegKeyword = aiResponse.keywords.some(
              (keyword) =>
                keyword.toLowerCase().includes("non-vegetarian") ||
                keyword.toLowerCase().includes("non-veg") ||
                keyword.toLowerCase().includes("meat") ||
                keyword.toLowerCase().includes("chicken") ||
                keyword.toLowerCase().includes("fish") ||
                keyword.toLowerCase().includes("mutton") ||
                keyword.toLowerCase().includes("beef") ||
                keyword.toLowerCase().includes("pork")
            );

            // If user specifically asked for vegetarian, only show veg dishes
            if (hasVegKeyword && !hasNonVegKeyword) {
              return dish.isVeg === true;
            }
            // If user specifically asked for non-vegetarian, only show non-veg dishes
            if (hasNonVegKeyword && !hasVegKeyword) {
              return dish.isVeg === false;
            }
          }

          return true; // Include all dishes if no specific dietary preference
        })
        .map((dish) => {
          const relevanceScore = dish.relevanceScore || 0;
          const dishId =
            dish._id?.toString() || dish.id?.toString() || "unknown";
          return {
            _id: dishId,
            name: dish.name || "Unknown Dish",
            price: dish.price || 0,
            isVeg: dish.isVeg || false,
            reason: `Recommended based on your request (${Math.round(
              relevanceScore * 100
            )}% match)`,
            dish: {
              _id: dish._id || dish.id,
              name: dish.name || "Unknown Dish",
              price: dish.price || 0,
              description: dish.description || "",
              category: dish.category?.name || "Other",
              image: dish.image || null,
            },
            relevanceScore: relevanceScore,
          };
        });
    } else {
      // Fall back to keyword-based filtering using AI response keywords
      recommendations = availableDishes
        .filter((dish) => {
          if (!dish || !dish._id || !dish.name) return false; // Filter out invalid dishes

          // Apply dietary preference filtering based on AI keywords
          if (aiResponse.keywords && aiResponse.keywords.length > 0) {
            const hasVegKeyword = aiResponse.keywords.some(
              (keyword) =>
                keyword.toLowerCase().includes("vegetarian") ||
                keyword.toLowerCase().includes("veg")
            );
            const hasNonVegKeyword = aiResponse.keywords.some(
              (keyword) =>
                keyword.toLowerCase().includes("non-vegetarian") ||
                keyword.toLowerCase().includes("non-veg") ||
                keyword.toLowerCase().includes("meat") ||
                keyword.toLowerCase().includes("chicken") ||
                keyword.toLowerCase().includes("fish") ||
                keyword.toLowerCase().includes("mutton") ||
                keyword.toLowerCase().includes("beef") ||
                keyword.toLowerCase().includes("pork")
            );

            // If user specifically asked for vegetarian, only show veg dishes
            if (hasVegKeyword && !hasNonVegKeyword) {
              if (dish.isVeg !== true) return false;
            }
            // If user specifically asked for non-vegetarian, only show non-veg dishes
            if (hasNonVegKeyword && !hasVegKeyword) {
              if (dish.isVeg !== false) return false;
            }
          }

          const searchText = `${dish.name || ""} ${dish.description || ""} ${
            dish.category?.name || ""
          }`.toLowerCase();
          return aiResponse.keywords && aiResponse.keywords.length > 0
            ? aiResponse.keywords.some((keyword) =>
                searchText.includes(keyword.toLowerCase())
              )
            : true; // If no keywords, include all dishes
        })
        .slice(0, 5) // Limit fallback results
        .map((dish) => ({
          _id: dish._id?.toString() || "unknown",
          name: dish.name || "Unknown Dish",
          price: dish.price || 0,
          isVeg: dish.isVeg || false,
          reason: `A perfect ${
            dish.category?.name?.toLowerCase() || "menu"
          } choice for you`,
          dish: {
            _id: dish._id,
            name: dish.name || "Unknown Dish",
            price: dish.price || 0,
            description: dish.description || "",
            category: dish.category?.name || "Other",
            image: dish.image || null,
          },
        }));
    }

    // If it was an add operation and we have dish details, add it to recommendations
    if (
      operationResult &&
      operationResult.success &&
      operationResult.operation === "add" &&
      operationResult.dish &&
      operationResult.dish._id
    ) {
      const dish = operationResult.dish;
      const dishId = dish._id?.toString() || "unknown";

      // Check if the dish is already in recommendations
      const existingIndex = recommendations.findIndex(
        (rec) => rec._id === dishId
      );

      if (existingIndex >= 0) {
        // Update the existing recommendation
        recommendations[existingIndex].reason = `Added to your cart`;
      } else {
        // Add to the beginning of recommendations
        recommendations.unshift({
          _id: dishId,
          name: dish.name || "Unknown Dish",
          price: dish.price || 0,
          reason: `Added to your cart`,
          isVeg: dish.isVeg || false,
          dish: {
            _id: dish._id,
            name: dish.name || "Unknown Dish",
            price: dish.price || 0,
            description: dish.description || "",
            category: dish.category?.name || "Other",
            image: dish.image || null,
          },
        });
      }
    }

    // Send cart operation event if applicable
    if (operationResult) {
      res.write(
        `event: cartOperation\ndata: ${JSON.stringify({
          type: "cartOperation",
          data: {
            result: operationResult,
            dishes: recommendations,
            faqSuggestions: aiResponse.faqSuggestions,
          },
        })}\n\n`
      );
    }

    // Send recommendation response
    res.write(
      `data: ${JSON.stringify({
        type: "recommendation",
        data: {
          recommendations,
          aiMessage: aiResponse.aiMessage,
          faqSuggestions: aiResponse.faqSuggestions,
          cartIntent: aiResponse.cartIntent,
          cartOperation: operationResult,
        },
      })}\n\n`
    );

    res.write(
      `data: ${JSON.stringify({
        type: "end",
        message: "\n",
        totalItems: recommendations.length,
      })}\n\n`
    );

    res.end();
  } catch (error) {
    console.error("Recommendation error:", error);
    res.write(
      `data: ${JSON.stringify({
        type: "error",
        message: "Error generating recommendations",
        error: error.message,
      })}\n\n`
    );
    res.end();
  }
};

export const getConversation = async (req, res) => {
  try {
    const { userId, outletId } = req.query;

    if (!userId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Both userId and outletId are required",
      });
    }

    // Find conversation using the compound index
    const conversation = await Conversation.findOne({
      userId,
      outletId,
    }).select("messages conversationId createdAt updatedAt");

    if (!conversation) {
      return res.status(200).json({
        success: true,
        data: {
          messages: [],
          conversationId: `${userId}_${outletId}`,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    }

    res.status(200).json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Error fetching conversation:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching conversation history",
      error: error.message,
    });
  }
};

export const getAllConversations = async (req, res) => {
  try {
    const { userId } = req.query;

    // Check if userId is null, "null", undefined, or empty string
    if (
      !userId ||
      userId === "null" ||
      userId === "undefined" ||
      userId === ""
    ) {
      // Return an empty array instead of an error for new users
      return res.status(200).json({
        success: true,
        data: [],
        message: "No conversations found for new user",
      });
    }

    // Validate that userId is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid userId format",
      });
    }

    // Find all conversations for the user
    const conversations = await Conversation.find({ userId })
      .populate("outletId", "name address") // Populate outlet details
      .select("messages conversationId createdAt updatedAt foodChainId")
      .sort({ updatedAt: -1 }); // Sort by most recent first

    // Format the response
    const formattedConversations = conversations.map((conv) => ({
      conversationId: conv.conversationId,
      outletName: conv.outletId?.name || "Unknown Outlet",
      outletAddress: conv.outletId?.address || "Unknown Address",
      lastMessage: conv.messages[conv.messages.length - 1] || null,
      messageCount: conv.messages.length,
      createdAt: conv.createdAt,
      updatedAt: conv.updatedAt,
      foodChainId: conv.foodChainId,
    }));

    res.status(200).json({
      success: true,
      data: formattedConversations,
    });
  } catch (error) {
    console.error("Error fetching all conversations:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching conversations",
      error: error.message,
    });
  }
};

export const deleteConversation = async (req, res) => {
  try {
    const { userId, outletId } = req.query;

    if (!userId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Both userId and outletId are required",
      });
    }

    // Find conversation using the compound index
    const conversation = await Conversation.findOne({
      userId,
      outletId,
    });

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: "Conversation not found",
      });
    }

    // Clear the messages array instead of deleting the conversation
    conversation.messages = [];
    conversation.updatedAt = new Date();
    await conversation.save();

    res.status(200).json({
      success: true,
      message: "Conversation cleared successfully",
      data: {
        conversationId: conversation.conversationId,
        messages: [],
        createdAt: conversation.createdAt,
        updatedAt: conversation.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error clearing conversation:", error);
    res.status(500).json({
      success: false,
      message: "Error clearing conversation",
      error: error.message,
    });
  }
};

export const getUserDetails = async (req, res) => {
  const id = req.params.id;
  console.log(req.params);

  if (!id) {
    return res.status(400).json({ message: "User ID is required" });
  }

  const user = await User.findById(id).select("-password -__v");

  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  res.status(200).json({
    success: true,
    data: user,
  });
};

export const updatePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword, isFirstLogin } = req.body;
    const userId = req.user.userId;

    if (!newPassword) {
      return res.status(400).json({ message: "New password is required" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // If it's not a first login, verify the old password
    if (!isFirstLogin) {
      if (!oldPassword) {
        return res.status(400).json({
          success: false,
          message: "Old password is required",
        });
      }

      if (!(await bcrypt.compare(oldPassword, user.password))) {
        return res.status(401).json({ message: "Old password is incorrect" });
      }

      if (await bcrypt.compare(newPassword, user.password)) {
        return res
          .status(400)
          .json({ message: "New password is same as old password" });
      }
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    user.password = hashedPassword;
    user.updatedAt = Date.now();

    // If this was a first login, clear the flag
    if (user.isFirstLogin) {
      user.isFirstLogin = false;
    }

    await user.save();

    res.status(200).json({
      success: true,
      message: "Password updated successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating password",
      error: error.message,
    });
  }
};

export const forgetPassword = async (req, res) => {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({ message: "Email is required" });
    }

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Generate reset token
    const resetToken = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: "1h",
    });

    // Send reset email (you can use a library like nodemailer for this)
    // ...

    res.status(200).json({
      success: true,
      message: "Password reset email sent",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error sending password reset email",
      error: error.message,
    });
  }
};

export const updateUserDetails = async (req, res) => {
  try {
    const { name, phone, address, aiMessage } = req.body;
    const userId = req.params.id;
    if (!name && !phone && !address && !aiMessage) {
      return res
        .status(400)
        .json({ message: "At least one field is required" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    if (name) user.name = name;
    if (phone) user.phone = phone;
    if (address) user.address = address;
    if (aiMessage) user.aiMessage = aiMessage;

    user.updatedAt = Date.now();
    await user.save();

    res.status(200).json({
      success: true,
      message: "User details updated successfully",
      data: user,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating user details",
      error: error.message,
    });
  }
};

export const createOrder = async (req, res) => {
  try {
    const {
      outletId,
      items,
      specialInstructions,
      tableNumber,
      couponCode,
      couponDiscount,
      finalAmount,
      appliedOffers, // Array of applied offers from frontend
    } = req.body;
    const userId = req.user._id;

    // Validate outlet
    const outlet = await Outlet.findById(outletId);
    if (!outlet) {
      return res.status(404).json({ message: "Outlet not found" });
    }

    // Calculate total amount and validate items
    let totalAmount = 0;
    const validatedItems = [];

    for (const item of items) {
      const dish = await Dish.findById(item._id);
      if (!dish || !dish.isAvailable) {
        return res.status(400).json({
          message: `Dish ${item._id} not found or not available`,
        });
      }

      validatedItems.push({
        dishId: dish._id,
        dishName: dish.name, // Store dish name for deleted dish handling
        quantity: item.quantity,
        price: dish.price,
      });

      totalAmount += dish.price * item.quantity;
    }

    // Process applied offers if provided
    let offerDiscount = 0;
    let processedAppliedOffers = [];

    if (appliedOffers && appliedOffers.length > 0) {
      try {
        // Validate and apply offers using the offer validation service
        const offerValidationResult = await validateAndApplyOffers(
          {
            outletId,
            items: validatedItems,
            totalAmount,
            foodChainId: outlet.foodChain,
          },
          userId
        );

        if (offerValidationResult.success) {
          // Filter only the offers that were actually applied from the frontend
          const frontendOfferIds = appliedOffers.map(
            (offer) => offer._id || offer.offerId
          );
          processedAppliedOffers = offerValidationResult.appliedOffers.filter(
            (appliedOffer) =>
              frontendOfferIds.includes(appliedOffer.offerId.toString())
          );

          // Calculate total offer discount
          offerDiscount = processedAppliedOffers.reduce(
            (sum, offer) => sum + offer.discount,
            0
          );
        }
      } catch (offerError) {
        console.error("Error processing offers:", offerError);
        // Continue with order creation even if offer processing fails
      }
    }

    // Order number will be generated by the pre-save hook

    // Create order with coupon and offer information
    const orderData = {
      userId,
      outletId,
      foodChainId: outlet.foodChain,
      items: validatedItems,
      totalAmount,
      specialInstructions,
      tableNumber,
      appliedOffers: processedAppliedOffers,
      offerDiscount,
    };

    // Add coupon information if provided
    if (couponCode) {
      orderData.couponCode = couponCode;
      orderData.couponDiscount = couponDiscount || 0;
    }

    // Calculate final amount including both coupon and offer discounts
    if (finalAmount !== undefined) {
      orderData.finalAmount = finalAmount;
    } else {
      const totalDiscounts = (couponDiscount || 0) + offerDiscount;
      orderData.finalAmount = Math.max(0, totalAmount - totalDiscounts);
    }

    const order = new Order(orderData);
    await order.save();

    // Emit socket event for real-time notification
    await emitNewOrder(order._id);

    res.status(201).json({
      success: true,
      message: "Order created successfully",
      data: order,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: "Error creating order",
      error: error.message,
    });
  }
};

export const getOrderHistory = async (req, res) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 20 } = req.query; // Increased default limit from 10 to 20

    const pageNum = parseInt(page);
    const limitNum = Math.min(parseInt(limit), 100); // Cap at 100 to prevent performance issues
    const skip = (pageNum - 1) * limitNum;

    // Get total count for pagination
    const totalOrders = await Order.countDocuments({ userId });

    const orders = await Order.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .populate("items.dishId", "name price")
      .populate("outletId", "name");

    res.status(200).json({
      success: true,
      data: orders,
      pagination: {
        total: totalOrders,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(totalOrders / limitNum),
        hasNext: pageNum < Math.ceil(totalOrders / limitNum),
        hasPrev: pageNum > 1,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching order history",
      error: error.message,
    });
  }
};

export const getOrderStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user._id;

    console.log(
      `Fetching order status for orderId: ${orderId}, userId: ${userId}`
    );

    // Validate orderId format
    if (!orderId.match(/^[0-9a-fA-F]{24}$/)) {
      console.error(`Invalid order ID format: ${orderId}`);
      return res.status(400).json({
        success: false,
        message: "Invalid order ID format",
      });
    }

    const order = await Order.findOne({ _id: orderId, userId })
      .populate("items.dishId", "name price")
      .populate("outletId", "name");

    if (!order) {
      console.log(`Order not found for orderId: ${orderId}, userId: ${userId}`);
      // Check if order exists but belongs to different user
      const orderExists = await Order.findById(orderId);
      if (orderExists) {
        return res.status(403).json({
          success: false,
          message: "Order not found or access denied",
        });
      }
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    console.log(`Order found successfully for orderId: ${orderId}`);
    res.status(200).json({
      success: true,
      data: order,
    });
  } catch (error) {
    console.error(
      `Error fetching order status for orderId: ${req.params.orderId}:`,
      error
    );
    res.status(500).json({
      success: false,
      message: "Error fetching order status",
      error: error.message,
    });
  }
};

export const cancelOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { cancellationReason } = req.body;
    const userId = req.user._id;

    const order = await Order.findOne({ _id: orderId, userId });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Check if order can be cancelled by customer
    // If order is in 'pending' state, customer can cancel directly
    // Otherwise, they can only request cancellation
    if (order.status !== "pending") {
      return res.status(400).json({
        success: false,
        message:
          "Orders can only be cancelled when in 'pending' state. Please contact the restaurant for orders that are already being prepared.",
      });
    }

    // Update order status to cancelled
    order.status = "cancelled";
    if (cancellationReason) {
      order.cancellationReason = cancellationReason;
    }

    await order.save();

    // Emit socket event for real-time update
    await emitOrderStatusUpdate(orderId);

    res.status(200).json({
      success: true,
      message: "Order cancelled successfully",
      data: order,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error cancelling order",
      error: error.message,
    });
  }
};

// Get outlets with location-based filtering and recommendations
export const getOutlets = async (req, res) => {
  try {
    const { city, pincode, search, page = 1, limit = 20 } = req.query;

    const pageNum = parseInt(page);
    const limitNum = Math.min(parseInt(limit), 50);
    const skip = (pageNum - 1) * limitNum;

    // Build query
    const query = { status: "active" };

    if (city) {
      query.city = { $regex: city, $options: "i" };
    }

    if (pincode) {
      query.pincode = pincode;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { address: { $regex: search, $options: "i" } },
        { city: { $regex: search, $options: "i" } },
      ];
    }

    // Get total count for pagination
    const totalOutlets = await Outlet.countDocuments(query);

    // Get outlets with food chain details
    const outlets = await Outlet.find(query)
      .populate("foodChain", "name theme")
      .select("name address city pincode contact isCloudKitchen deliveryRadius")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    res.status(200).json({
      success: true,
      data: outlets,
      pagination: {
        total: totalOutlets,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(totalOutlets / limitNum),
        hasNext: pageNum < Math.ceil(totalOutlets / limitNum),
        hasPrev: pageNum > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching outlets:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching outlets",
      error: error.message,
    });
  }
};

// Get outlet recommendations based on user preferences
export const getOutletRecommendations = async (req, res) => {
  try {
    const { city, pincode, userPreferences } = req.query;
    const userId = req.user?._id;

    // Build base query for active outlets
    const query = { status: "active" };

    if (city) {
      query.city = { $regex: city, $options: "i" };
    }

    if (pincode) {
      query.pincode = pincode;
    }

    // Get outlets with food chain details and dishes
    let outlets = await Outlet.find(query)
      .populate("foodChain", "name theme")
      .populate("dishes", "name category cuisine isVeg price")
      .select(
        "name address city pincode contact isCloudKitchen deliveryRadius dishes"
      )
      .limit(50); // Increased limit for better filtering

    let personalizedOutlets = [];

    // If user is logged in, get their detailed order history for personalization
    if (userId) {
      try {
        // Get user's order history with dish details
        const userOrders = await Order.find({ userId })
          .populate({
            path: "items.dishId",
            select: "name category cuisine isVeg price",
          })
          .populate("outletId", "_id name")
          .select("outletId items totalAmount createdAt")
          .sort({ createdAt: -1 })
          .limit(50);

        // Analyze user preferences from order history
        const userAnalysis = analyzeUserPreferences(userOrders);

        // Score outlets based on user preferences
        personalizedOutlets = outlets.map((outlet) => {
          const score = calculateOutletScore(outlet, userAnalysis, userOrders);
          return {
            ...outlet.toObject(),
            recommendationScore: score.total,
            recommendationReasons: score.reasons,
            matchingCuisines: score.matchingCuisines,
            priceMatch: score.priceMatch,
          };
        });

        // Sort by recommendation score
        personalizedOutlets.sort(
          (a, b) => b.recommendationScore - a.recommendationScore
        );
      } catch (error) {
        console.error("Error in personalization:", error);
        // Fallback to basic recommendations
        personalizedOutlets = outlets.map((outlet) => ({
          ...outlet.toObject(),
          recommendationScore: 0,
          recommendationReasons: ["General recommendation"],
        }));
      }
    } else {
      // For non-logged-in users, provide general recommendations
      personalizedOutlets = outlets.map((outlet) => {
        const score = calculateGeneralOutletScore(outlet);
        return {
          ...outlet.toObject(),
          recommendationScore: score.total,
          recommendationReasons: score.reasons,
        };
      });

      personalizedOutlets.sort(
        (a, b) => b.recommendationScore - a.recommendationScore
      );
    }

    // Limit final results
    const finalOutlets = personalizedOutlets.slice(0, 20);

    res.status(200).json({
      success: true,
      data: finalOutlets,
      message: userId
        ? "Personalized recommendations based on your preferences"
        : "General recommendations",
      metadata: userId
        ? {
            totalAnalyzed: outlets.length,
            personalized: true,
          }
        : {
            totalAnalyzed: outlets.length,
            personalized: false,
          },
    });
  } catch (error) {
    console.error("Error fetching outlet recommendations:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching outlet recommendations",
      error: error.message,
    });
  }
};

// Rate limiting for order updates (in-memory store for simplicity)
const orderUpdateLocks = new Map();

export const updateOrderItems = async (req, res) => {
  const { orderId } = req.params; // Move outside try block for error handling

  try {
    const { items, couponCode, couponDiscount, appliedOffers } = req.body;
    const userId = req.user._id;

    // Check if order is currently being updated
    if (orderUpdateLocks.has(orderId)) {
      return res.status(429).json({
        success: false,
        message: "Order is currently being updated. Please wait and try again.",
      });
    }

    // Lock the order for updates
    orderUpdateLocks.set(orderId, Date.now());

    // Find the order
    const order = await Order.findOne({ _id: orderId, userId });
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Check if order can be updated (only if payment is not completed)
    if (order.paymentStatus === "paid") {
      return res.status(400).json({
        success: false,
        message: "Cannot update order after payment is completed",
      });
    }

    // Validate items
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Items are required",
      });
    }

    // Calculate new total and validate items
    let totalAmount = 0;
    const validatedItems = [];

    for (const item of items) {
      if (!item.dishId || !item.quantity || !item.price) {
        return res.status(400).json({
          success: false,
          message: "Each item must have dishId, quantity, and price",
        });
      }

      // If dishName is not provided, fetch it from the database
      let dishName = item.dishName;
      if (!dishName) {
        const dish = await Dish.findById(item.dishId);
        if (dish) {
          dishName = dish.name;
        } else {
          // If dish is not found, use a fallback name
          dishName = "Unknown Dish";
        }
      }

      validatedItems.push({
        ...item,
        dishName,
      });

      totalAmount += item.price * item.quantity;
    }

    // Update order items while preserving served quantities
    order.items = validatedItems.map((item) => {
      // Find existing item to preserve served status
      const existingItem = order.items.find(
        (existing) => existing.dishId.toString() === item.dishId.toString()
      );

      const servedQuantity = existingItem
        ? existingItem.servedQuantity || 0
        : 0;
      const newQuantity = item.quantity;

      // If new quantity is less than served quantity, that's not allowed (should be caught by frontend)
      if (newQuantity < servedQuantity) {
        throw new Error(
          `Cannot reduce quantity below served amount for dish ${item.dishId}`
        );
      }

      return {
        dishId: item.dishId,
        dishName: item.dishName || existingItem?.dishName, // Use provided dishName or preserve existing one
        quantity: newQuantity,
        price: item.price,
        isServed: servedQuantity > 0 && servedQuantity === newQuantity, // Fully served if all quantities are served
        servedQuantity: servedQuantity,
        servedAt: existingItem?.servedAt,
        servedBy: existingItem?.servedBy,
      };
    });
    order.totalAmount = totalAmount;
    order.modificationReason = "Items updated by customer";

    // Process applied offers if provided
    let offerDiscount = 0;
    let processedAppliedOffers = [];

    if (appliedOffers && appliedOffers.length > 0) {
      try {
        // Validate and apply offers using the offer validation service
        const offerValidationResult = await validateAndApplyOffers(
          {
            outletId: order.outletId,
            items: validatedItems,
            totalAmount,
            foodChainId: order.foodChainId,
          },
          userId
        );

        if (offerValidationResult.success) {
          // Filter only the offers that were actually applied from the frontend
          const frontendOfferIds = appliedOffers.map(
            (offer) => offer._id || offer.offerId
          );
          processedAppliedOffers = offerValidationResult.appliedOffers.filter(
            (appliedOffer) =>
              frontendOfferIds.includes(appliedOffer.offerId.toString())
          );

          // Calculate total offer discount
          offerDiscount = processedAppliedOffers.reduce(
            (sum, offer) => sum + offer.discount,
            0
          );
        }
      } catch (offerError) {
        console.error(
          "Error processing offers during order update:",
          offerError
        );
        // Continue with order update even if offer processing fails
      }
    }

    // Update order with offer information
    order.appliedOffers = processedAppliedOffers;
    order.offerDiscount = offerDiscount;

    // Automatically reapply coupon if one exists, or apply new coupon if provided
    if (couponCode) {
      // If couponDiscount is provided, use it; otherwise recalculate
      if (couponDiscount !== undefined) {
        order.couponCode = couponCode;
        order.couponDiscount = couponDiscount;
        order.finalAmount = Math.max(
          0,
          totalAmount - couponDiscount - offerDiscount
        );
      } else {
        // Set the coupon code and let reapplyCouponToOrder calculate the discount
        order.couponCode = couponCode;
        const couponResult = await reapplyCouponToOrder(order);
        if (!couponResult.success) {
          console.warn("Failed to apply coupon:", couponResult.message);
          order.couponCode = undefined;
          order.couponDiscount = 0;
          order.finalAmount = totalAmount;
        }
      }
    } else if (order.couponCode) {
      // Automatically reapply existing coupon with new total
      const couponResult = await reapplyCouponToOrder(order);
      if (!couponResult.success) {
        console.warn("Failed to reapply coupon:", couponResult.message);
      }
    } else {
      order.couponCode = undefined;
      order.couponDiscount = 0;
      order.finalAmount = Math.max(0, totalAmount - offerDiscount);
    }

    order.updatedAt = new Date();
    await order.save();

    // Update payment link if it exists and payment is not completed
    if (order.paymentStatus !== "paid") {
      try {
        const existingPayment = await Payment.findOne({ orderId });
        if (existingPayment && existingPayment.razorpayPaymentLinkId) {
          // Populate order with user information for payment link creation
          const populatedOrder = await Order.findById(orderId).populate(
            "userId",
            "name phone email"
          );

          const updatedPaymentLink = await updatePaymentLink(
            existingPayment.razorpayPaymentLinkId,
            populatedOrder || order
          );

          // Update payment record with new payment link details
          existingPayment.razorpayPaymentLinkId = updatedPaymentLink.id;
          existingPayment.paymentLink = updatedPaymentLink.short_url;
          existingPayment.amount = order.finalAmount || order.totalAmount;
          await existingPayment.save();
        }
      } catch (error) {
        console.error("Error updating payment link:", error);
        // Don't fail the order update if payment link update fails
      }
    }

    // Emit socket event for real-time notification
    await emitOrderStatusUpdate(orderId);

    // Emit order items update event
    await emitOrderItemsUpdate(orderId, "user");

    // Release the lock
    orderUpdateLocks.delete(orderId);

    res.status(200).json({
      success: true,
      message: "Order updated successfully",
      data: order,
    });
  } catch (error) {
    console.error(error);

    // Release the lock on error
    orderUpdateLocks.delete(orderId);

    res.status(500).json({
      success: false,
      message: "Error updating order",
      error: error.message,
    });
  }
};

// Helper function to analyze user preferences from order history
const analyzeUserPreferences = (userOrders) => {
  const analysis = {
    cuisines: {},
    categories: {},
    priceRanges: { low: 0, medium: 0, high: 0 },
    vegPreference: { veg: 0, nonVeg: 0 },
    frequentOutlets: {},
    averageOrderValue: 0,
    totalOrders: userOrders.length,
    totalSpent: 0,
  };

  let totalItems = 0;
  let totalSpent = 0;

  userOrders.forEach((order) => {
    // Track frequent outlets
    if (order.outletId && order.outletId._id) {
      const outletId = order.outletId._id.toString();
      analysis.frequentOutlets[outletId] =
        (analysis.frequentOutlets[outletId] || 0) + 1;
    }

    totalSpent += order.totalAmount || 0;

    order.items.forEach((item) => {
      if (item.dishId) {
        totalItems++;

        // Analyze cuisines
        if (item.dishId.cuisine) {
          analysis.cuisines[item.dishId.cuisine] =
            (analysis.cuisines[item.dishId.cuisine] || 0) + item.quantity;
        }

        // Analyze categories
        if (item.dishId.category) {
          const categoryName =
            typeof item.dishId.category === "object"
              ? item.dishId.category.name
              : item.dishId.category;
          analysis.categories[categoryName] =
            (analysis.categories[categoryName] || 0) + item.quantity;
        }

        // Analyze price preferences
        const price = item.dishId.price || 0;
        if (price < 200) {
          analysis.priceRanges.low += item.quantity;
        } else if (price < 500) {
          analysis.priceRanges.medium += item.quantity;
        } else {
          analysis.priceRanges.high += item.quantity;
        }

        // Analyze veg preference
        if (item.dishId.isVeg) {
          analysis.vegPreference.veg += item.quantity;
        } else {
          analysis.vegPreference.nonVeg += item.quantity;
        }
      }
    });
  });

  analysis.totalSpent = totalSpent;
  analysis.averageOrderValue =
    userOrders.length > 0 ? totalSpent / userOrders.length : 0;

  // Convert to percentages
  if (totalItems > 0) {
    Object.keys(analysis.cuisines).forEach((cuisine) => {
      analysis.cuisines[cuisine] =
        (analysis.cuisines[cuisine] / totalItems) * 100;
    });

    Object.keys(analysis.categories).forEach((category) => {
      analysis.categories[category] =
        (analysis.categories[category] / totalItems) * 100;
    });

    analysis.priceRanges.low = (analysis.priceRanges.low / totalItems) * 100;
    analysis.priceRanges.medium =
      (analysis.priceRanges.medium / totalItems) * 100;
    analysis.priceRanges.high = (analysis.priceRanges.high / totalItems) * 100;

    analysis.vegPreference.veg =
      (analysis.vegPreference.veg / totalItems) * 100;
    analysis.vegPreference.nonVeg =
      (analysis.vegPreference.nonVeg / totalItems) * 100;
  }

  return analysis;
};

// Helper function to calculate outlet score based on user preferences
const calculateOutletScore = (outlet, userAnalysis, userOrders) => {
  let score = 0;
  const reasons = [];
  const matchingCuisines = [];
  let priceMatch = 0;

  // Base score for active outlet
  score += 10;

  // Check if user has visited this outlet before
  const outletId = outlet._id.toString();
  const visitCount = userAnalysis.frequentOutlets[outletId] || 0;
  if (visitCount > 0) {
    score += visitCount * 15; // High boost for previously visited outlets
    reasons.push(
      `You've ordered from here ${visitCount} time${
        visitCount > 1 ? "s" : ""
      } before`
    );
  }

  // Analyze outlet's dishes for cuisine and price matching
  if (outlet.dishes && outlet.dishes.length > 0) {
    const outletCuisines = {};
    const outletPrices = { low: 0, medium: 0, high: 0 };
    const outletVegOptions = { veg: 0, nonVeg: 0 };

    outlet.dishes.forEach((dish) => {
      // Count cuisines
      if (dish.cuisine) {
        outletCuisines[dish.cuisine] = (outletCuisines[dish.cuisine] || 0) + 1;
      }

      // Count price ranges
      const price = dish.price || 0;
      if (price < 200) {
        outletPrices.low++;
      } else if (price < 500) {
        outletPrices.medium++;
      } else {
        outletPrices.high++;
      }

      // Count veg options
      if (dish.isVeg) {
        outletVegOptions.veg++;
      } else {
        outletVegOptions.nonVeg++;
      }
    });

    // Score based on cuisine preferences
    Object.keys(userAnalysis.cuisines).forEach((userCuisine) => {
      if (outletCuisines[userCuisine]) {
        const userPreference = userAnalysis.cuisines[userCuisine];
        const outletCoverage =
          (outletCuisines[userCuisine] / outlet.dishes.length) * 100;
        const cuisineScore = (userPreference * outletCoverage) / 100;
        score += cuisineScore;

        if (cuisineScore > 5) {
          matchingCuisines.push(userCuisine);
          reasons.push(
            `Offers ${userCuisine} cuisine (${Math.round(
              userPreference
            )}% of your orders)`
          );
        }
      }
    });

    // Score based on price preferences
    const totalDishes = outlet.dishes.length;
    if (totalDishes > 0) {
      const outletPriceDistribution = {
        low: (outletPrices.low / totalDishes) * 100,
        medium: (outletPrices.medium / totalDishes) * 100,
        high: (outletPrices.high / totalDishes) * 100,
      };

      // Calculate price match score
      priceMatch =
        userAnalysis.priceRanges.low * outletPriceDistribution.low +
        userAnalysis.priceRanges.medium * outletPriceDistribution.medium +
        userAnalysis.priceRanges.high * outletPriceDistribution.high;

      score += (priceMatch / 100) * 10; // Convert to score out of 10

      if (priceMatch > 30) {
        reasons.push(
          `Price range matches your preferences (${Math.round(
            priceMatch
          )}% match)`
        );
      }
    }

    // Score based on veg preference
    if (
      userAnalysis.vegPreference.veg > 60 &&
      outletVegOptions.veg > outletVegOptions.nonVeg
    ) {
      score += 15;
      reasons.push("Great vegetarian options");
    } else if (
      userAnalysis.vegPreference.nonVeg > 60 &&
      outletVegOptions.nonVeg > outletVegOptions.veg
    ) {
      score += 15;
      reasons.push("Great non-vegetarian options");
    }
  }

  // Boost for cloud kitchens if user orders frequently (convenience factor)
  if (outlet.isCloudKitchen && userAnalysis.totalOrders > 5) {
    score += 5;
    reasons.push("Convenient delivery option");
  }

  // Add some randomness to avoid always showing the same outlets
  score += Math.random() * 3;

  return {
    total: Math.round(score * 10) / 10,
    reasons: reasons.slice(0, 3), // Limit to top 3 reasons
    matchingCuisines,
    priceMatch: Math.round(priceMatch),
  };
};

// Helper function for general outlet scoring (non-personalized)
const calculateGeneralOutletScore = (outlet) => {
  let score = 10; // Base score
  const reasons = [];

  // Score based on variety of dishes
  if (outlet.dishes && outlet.dishes.length > 0) {
    score += Math.min(outlet.dishes.length / 10, 10); // Up to 10 points for variety

    if (outlet.dishes.length > 20) {
      reasons.push("Wide variety of dishes");
    }

    // Check for diverse cuisines
    const cuisines = new Set(
      outlet.dishes.map((dish) => dish.cuisine).filter(Boolean)
    );
    if (cuisines.size > 3) {
      score += 5;
      reasons.push("Multiple cuisine options");
    }

    // Check for balanced veg/non-veg options
    const vegCount = outlet.dishes.filter((dish) => dish.isVeg).length;
    const nonVegCount = outlet.dishes.length - vegCount;
    if (vegCount > 0 && nonVegCount > 0) {
      score += 3;
      reasons.push("Both veg and non-veg options");
    }
  }

  // Slight boost for cloud kitchens (delivery convenience)
  if (outlet.isCloudKitchen) {
    score += 2;
    reasons.push("Delivery available");
  }

  // Add randomness for variety
  score += Math.random() * 5;

  return {
    total: Math.round(score * 10) / 10,
    reasons: reasons.slice(0, 2),
  };
};

// Configure Google OAuth Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: "/api/v1/user/auth/google/callback",
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        // Check if user already exists with this Google ID
        let user = await User.findOne({ googleId: profile.id });

        if (user) {
          return done(null, user);
        }

        // Check if user exists with the same email
        user = await User.findOne({ email: profile.emails[0].value });

        if (user) {
          // Link Google account to existing user
          user.googleId = profile.id;
          await user.save();
          return done(null, user);
        }

        // Create new user
        user = new User({
          googleId: profile.id,
          name: profile.displayName,
          email: profile.emails[0].value,
          role: "user",
          createdBy: "google",
        });

        await user.save();
        return done(null, user);
      } catch (error) {
        return done(error, null);
      }
    }
  )
);

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user._id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findById(id);
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Google OAuth routes
export const googleAuth = passport.authenticate("google", {
  scope: ["profile", "email"],
});

export const googleCallback = async (req, res) => {
  try {
    const user = req.user;

    if (!user) {
      return res.redirect(
        `${process.env.FRONTEND_URL}/auth?error=google_auth_failed`
      );
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET
    );

    // Redirect to frontend with token
    res.redirect(
      `${
        process.env.FRONTEND_URL
      }/auth/google/success?token=${token}&user=${encodeURIComponent(
        JSON.stringify({
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
        })
      )}`
    );
  } catch (error) {
    console.error("Google callback error:", error);
    res.redirect(`${process.env.FRONTEND_URL}/auth?error=google_auth_failed`);
  }
};

// Manual Google token verification (for frontend-initiated Google auth)
export const verifyGoogleToken = async (req, res) => {
  try {
    const { token, userData } = req.body;

    if (!token || !userData) {
      return res.status(400).json({
        success: false,
        message: "Google token and user data are required",
      });
    }

    // In a production environment, you should verify the token with Google
    // For now, we'll trust the frontend verification
    const { email, name, googleId } = userData;

    if (!email || !name || !googleId) {
      return res.status(400).json({
        success: false,
        message: "Invalid user data from Google",
      });
    }

    // Check if user already exists with this Google ID
    let user = await User.findOne({ googleId });

    if (user) {
      // User exists, generate token and return
      const jwtToken = jwt.sign(
        { userId: user._id, email: user.email },
        process.env.JWT_SECRET
      );

      return res.status(200).json({
        success: true,
        message: "Login successful",
        data: {
          token: jwtToken,
          user: {
            id: user._id,
            name: user.name,
            email: user.email,
            role: user.role,
            createdBy: user.createdBy,
          },
        },
      });
    }

    // Check if user exists with the same email
    user = await User.findOne({ email });

    if (user) {
      // Link Google account to existing user
      user.googleId = googleId;
      await user.save();

      const jwtToken = jwt.sign(
        { userId: user._id, email: user.email },
        process.env.JWT_SECRET
      );

      return res.status(200).json({
        success: true,
        message: "Account linked and login successful",
        data: {
          token: jwtToken,
          user: {
            id: user._id,
            name: user.name,
            email: user.email,
            role: user.role,
            createdBy: user.createdBy,
          },
        },
      });
    }

    // Create new user
    user = new User({
      googleId,
      name,
      email,
      role: "user",
      createdBy: "google",
    });

    await user.save();

    const jwtToken = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET
    );

    res.status(201).json({
      success: true,
      message: "Registration successful",
      data: {
        token: jwtToken,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          createdBy: user.createdBy,
        },
      },
    });
  } catch (error) {
    console.error("Google token verification error:", error);
    res.status(500).json({
      success: false,
      message: "Error verifying Google token",
      error: error.message,
    });
  }
};
