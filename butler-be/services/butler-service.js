/**
 * Detect the language of user message
 * @param {string} userMessage - The user's message
 * @param {Array} conversationHistory - Previous conversation messages
 * @returns {string} - Detected language code (hi, en, hi-en)
 */
export const detectLanguage = (userMessage, conversationHistory = []) => {
  // Simple language detection based on script and common words
  const hindiPattern = /[\u0900-\u097F]/; // Devanagari script
  const englishPattern = /^[a-zA-Z\s.,!?'"()-]+$/;

  // Check for Hindi script
  if (hindiPattern.test(userMessage)) {
    // Check if it's mixed (Hinglish)
    if (englishPattern.test(userMessage.replace(/[\u0900-\u097F]/g, ""))) {
      return "hi-en"; // Hinglish
    }
    return "hi"; // Pure Hindi
  }

  // Check conversation history for language context
  if (conversationHistory && conversationHistory.length > 0) {
    // Handle both array and string formats
    let messages = [];
    if (Array.isArray(conversationHistory)) {
      messages = conversationHistory;
    } else if (typeof conversationHistory === "string") {
      try {
        messages = JSON.parse(conversationHistory);
      } catch (error) {
        console.log(
          "Could not parse conversation history, skipping language context"
        );
        messages = [];
      }
    }

    if (messages.length > 0) {
      const recentMessages = messages.slice(-3);
      const hasHindi = recentMessages.some((msg) => {
        const messageText = msg.message || msg.text || msg.content || "";
        return hindiPattern.test(messageText);
      });
      if (hasHindi) {
        return "hi-en"; // Likely Hinglish context
      }
    }
  }

  // Common Hinglish words/patterns
  const hinglishWords = [
    "hai",
    "kya",
    "acha",
    "theek",
    "bhai",
    "yaar",
    "karo",
    "karna",
    "chahiye",
  ];
  const hasHinglishWords = hinglishWords.some((word) =>
    userMessage.toLowerCase().includes(word)
  );

  if (hasHinglishWords) {
    return "hi-en"; // Hinglish
  }

  return "en"; // Default to English
};

/**
 * Get language-specific response instructions
 * @param {string} languageCode - Detected language code
 * @returns {string} - Language-specific instructions for AI
 */
export const getLanguageInstructions = (languageCode) => {
  const instructions = {
    hi: `
    LANGUAGE INSTRUCTIONS:
    - Respond ONLY in Hindi using Devanagari script
    - Use formal, respectful Hindi (आप form)
    - Maintain butler etiquette in Hindi
    - Use proper Hindi food terminology
    - Example: "मैं आपके लिए मेन्यू से बेहतरीन विकल्प सुझा सकता हूँ"`,

    en: `
    LANGUAGE INSTRUCTIONS:
    - Respond ONLY in English
    - Use formal, professional English
    - Maintain butler etiquette
    - Use proper English food terminology
    - Example: "I can suggest excellent options from our menu for you"`,

    "hi-en": `
    LANGUAGE INSTRUCTIONS:
    - Respond in Hinglish (Hindi-English mix)
    - Use casual but respectful tone
    - Mix Hindi and English naturally
    - Use common Hinglish food terms
    - Example: "Main aapke liye menu se best options suggest kar sakta hun"`,
  };

  return instructions[languageCode] || instructions["en"];
};

export const generateRecommendation = async (
  userMessage,
  menu,
  lastConversation,
  cartHistory = [],
  cartOperation = null,
  userAiMessage = "",
  comprehensiveContext = null,
  selectedLanguage = null
) => {
  // Use selected language if provided, otherwise detect from message
  const detectedLanguage =
    selectedLanguage || detectLanguage(userMessage, lastConversation);
  const languageInstructions = getLanguageInstructions(detectedLanguage);

  // Generate context-aware prompt addition
  let contextPrompt = "";
  if (comprehensiveContext) {
    try {
      const { generateContextPrompt } = await import("./context-service.js");
      contextPrompt = generateContextPrompt(comprehensiveContext);
    } catch (error) {
      console.error("Error generating context prompt:", error);
      contextPrompt = "";
    }
  }

  const prompt = `You are a sophisticated multilingual butler at a high-end restaurant. Your task is to understand the customer's request and provide relevant recommendations from the available menu. You must respond in the SAME LANGUAGE as the user's message.

  ${languageInstructions}

  Here's the available menu in JSON format:
  ${menu}

  Here's the conversation history:
  ${JSON.stringify(lastConversation)}

  Here's the user's cart history:
  ${JSON.stringify(cartHistory)}

  ${
    cartOperation
      ? `The user has performed a cart operation: ${JSON.stringify(
          cartOperation
        )}`
      : ""
  }

  ${contextPrompt}

  Based on the user's message, previous conversation, and cart history, please provide:
  1. Keywords that match menu items (for search)
  2. A helpful response that acknowledges any cart operations if performed
  3. Suggested follow-up questions which user could ask related to your response. It is required to be from user's perspective.

  IMPORTANT CART OPERATION INSTRUCTIONS:
  - If the user asks to add an item to their cart, inform them that you found the item and guide them to use the + button
  - If the user asks to remove an item from their cart, guide them to use the - button
  - If the user asks to clear their cart, explain that they can remove items individually using the - button
  - If the user asks about their cart contents, explain that they can view their cart by clicking the cart icon
  - If the user asks to modify an item in their cart, explain that they need to remove the item and add it again with the desired modifications

  IMPORTANT RESPONSE GUIDELINES:
  - Keep responses concise and professional
  - Use only terms that match the available menu
  - For unavailable items, acknowledge it and suggest alternatives from the menu
  - Maintain formal butler etiquette
  - No small talk or unnecessary commentary
  - If the user asks for a specific dish, check if it's in the menu and respond accordingly
  - If a cart operation was performed, make that the primary focus of your response
  - Strictly follow the format of the JSON response so that the frontend can parse it easily
  - RESPOND IN THE SAME LANGUAGE AS THE USER'S MESSAGE

  Format your response as valid JSON:
  {
    "keywords": ["keyword1", "keyword2", ...],
    "aiMessage": "Your helpful response here IN THE USER'S LANGUAGE",
    "faqSuggestions": ["Question 1 IN USER'S LANGUAGE?", "Question 2 IN USER'S LANGUAGE?", ...],
    "cartIntent": {
      "detected": true/false,
      "operation": "add"/"remove"/"clear"/"view",
      "item": "item name",
      "quantity": number
    },
    "detectedLanguage": "${detectedLanguage}"
  }

  IMPORTANT:
  - Keywords must ALWAYS be in English only (for search functionality)
  - aiMessage and faqSuggestions should be in the user's detected language
  - Ensure the JSON is complete and properly formatted

  User message: ${userMessage}
  User's default message for ai message: ${userAiMessage}`;

  return prompt;
};

export const generateVectorSearchPrompt = async (
  userMessage,
  vectorResults,
  lastConversation,
  cartHistory = [],
  cartOperation = null,
  comprehensiveContext = null,
  selectedLanguage = null
) => {
  // Use selected language if provided, otherwise detect from message
  const detectedLanguage =
    selectedLanguage || detectLanguage(userMessage, lastConversation);
  const languageInstructions = getLanguageInstructions(detectedLanguage);

  // Generate context-aware prompt addition
  let contextPrompt = "";
  if (comprehensiveContext) {
    try {
      const { generateContextPrompt } = await import("./context-service.js");
      contextPrompt = generateContextPrompt(comprehensiveContext);
    } catch (error) {
      console.error("Error generating context prompt:", error);
      contextPrompt = "";
    }
  }

  const prompt = `You are a sophisticated multilingual butler at a high-end restaurant. Your task is to understand the customer's request and provide relevant recommendations from the available menu. You must respond in the SAME LANGUAGE as the user's message.

  ${languageInstructions}

  Here are the most relevant menu items based on vector search:
  ${JSON.stringify(vectorResults)}

  Here's the conversation history:
  ${JSON.stringify(lastConversation)}

  Here's the user's cart history:
  ${JSON.stringify(cartHistory)}

  ${
    cartOperation
      ? `The user has performed a cart operation: ${JSON.stringify(
          cartOperation
        )}`
      : ""
  }

  ${contextPrompt}

  Based on the user's message, previous conversation, and the vector search results, please provide:
  1. Keywords that match menu items (for search)
  2. A helpful response that recommends items from the vector search results and acknowledges any cart operations
  3. Suggested follow-up questions

  IMPORTANT CART OPERATION INSTRUCTIONS:
  - If the user asks to add an item to their cart, inform them that you found the item and guide them to use the + button
  - If the user asks to remove an item from their cart, guide them to use the - button
  - If the user asks to clear their cart, explain that they can remove items individually using the - button
  - If the user asks about their cart contents, explain that they can view their cart by clicking the cart icon
  - If the user asks to modify an item in their cart, explain that they need to remove the item and add it again with the desired modifications

  IMPORTANT RESPONSE GUIDELINES:
  - Keep responses concise and professional
  - Use only terms that match the available menu
  - For unavailable items, acknowledge it and suggest alternatives from the menu
  - Maintain formal butler etiquette
  - No small talk or unnecessary commentary
  - If the user asks for a specific dish, check if it's in the vector results and respond accordingly
  - If a cart operation was performed, make that the primary focus of your response
  - RESPOND IN THE SAME LANGUAGE AS THE USER'S MESSAGE

  Format your response as valid JSON:
  {
    "keywords": ["keyword1", "keyword2", ...],
    "aiMessage": "Your helpful response here IN THE USER'S LANGUAGE",
    "faqSuggestions": ["Question 1 IN USER'S LANGUAGE?", "Question 2 IN USER'S LANGUAGE?", ...],
    "cartIntent": {
      "detected": true/false,
      "operation": "add"/"remove"/"clear"/"view",
      "item": "item name",
      "quantity": number
    },
    "detectedLanguage": "${detectedLanguage}"
  }

  IMPORTANT:
  - Keywords must ALWAYS be in English only (for search functionality)
  - aiMessage and faqSuggestions should be in the user's detected language
  - Ensure the JSON is complete and properly formatted

  User message: ${userMessage}`;

  return prompt;
};
